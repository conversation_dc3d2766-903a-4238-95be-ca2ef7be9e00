"""
Proxy Configuration Settings for Auto Gmail Creator

This file contains all proxy-related configuration options.
Modify these settings to customize proxy behavior.
"""

# =============================================================================
# MAIN PROXY SETTINGS
# =============================================================================

# Enable or disable proxy usage entirely
USE_PROXY = True

# Prefer HTTP proxy over SOCKS proxy
PREFER_HTTP_PROXY = True

# Maximum number of proxy attempts before giving up
MAX_PROXY_ATTEMPTS = 5

# Timeout for proxy connectivity tests (seconds)
PROXY_TEST_TIMEOUT = 8

# Timeout for FreeProxy library (seconds)
FREEPROXY_TIMEOUT = 5

# =============================================================================
# SOCKS PROXY SETTINGS
# =============================================================================

# SOCKS proxy configuration (set to None to disable)
# Format: "socks5://username:password@host:port"
SOCKS_PROXY = None

# Example SOCKS proxy configurations (uncomment and modify as needed):
# SOCKS_PROXY = "socks5://user:<EMAIL>:1080"
# SOCKS_PROXY = "socks5://**************:59166"  # No auth required

# =============================================================================
# HTTP PROXY SETTINGS
# =============================================================================

# Custom HTTP proxy (set to None to use automatic proxy discovery)
# Format: "http://host:port" or "*****************************:port"
CUSTOM_HTTP_PROXY = None

# Example HTTP proxy configurations (uncomment and modify as needed):
# CUSTOM_HTTP_PROXY = "http://proxy.example.com:8080"
# CUSTOM_HTTP_PROXY = "http://user:<EMAIL>:8080"

# =============================================================================
# FREEPROXY LIBRARY SETTINGS
# =============================================================================

# Enable FreeProxy library for automatic proxy discovery
USE_FREEPROXY = True

# FreeProxy country filter (None for any country)
# Examples: 'US', 'GB', 'DE', 'FR', etc.
FREEPROXY_COUNTRY = None

# FreeProxy protocol filter
# Options: 'http', 'https', 'socks4', 'socks5'
FREEPROXY_PROTOCOL = 'http'

# Enable random proxy selection from FreeProxy
FREEPROXY_RANDOM = True

# =============================================================================
# PROXY DATABASE SETTINGS
# =============================================================================

# Enable proxy database as fallback
USE_PROXY_DATABASE = True

# Path to proxy database CSV file
PROXY_DATABASE_PATH = "./data/Proxy_DB.csv"

# =============================================================================
# CHROME DRIVER PROXY SETTINGS
# =============================================================================

# Additional Chrome arguments for proxy compatibility
CHROME_PROXY_ARGS = [
    '--proxy-server="direct://"',  # Bypass proxy for certain connections
    '--proxy-bypass-list="localhost,127.0.0.1"',
    '--ignore-ssl-errors=yes',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors-spki-list',
    '--disable-web-security',
]

# Disable SSL verification for proxy compatibility
DISABLE_SSL_VERIFICATION = True

# =============================================================================
# FALLBACK SETTINGS
# =============================================================================

# Enable fallback to direct connection if all proxies fail
ENABLE_DIRECT_FALLBACK = True

# Retry failed proxy connections
RETRY_FAILED_PROXIES = False

# =============================================================================
# DEBUGGING SETTINGS
# =============================================================================

# Enable verbose proxy logging
VERBOSE_PROXY_LOGGING = True

# Test proxy connectivity before use
TEST_PROXY_BEFORE_USE = True

# Log proxy test results
LOG_PROXY_TESTS = True

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_proxy_config():
    """Get current proxy configuration as a dictionary"""
    return {
        'use_proxy': USE_PROXY,
        'prefer_http_proxy': PREFER_HTTP_PROXY,
        'max_proxy_attempts': MAX_PROXY_ATTEMPTS,
        'proxy_test_timeout': PROXY_TEST_TIMEOUT,
        'freeproxy_timeout': FREEPROXY_TIMEOUT,
        'socks_proxy': SOCKS_PROXY,
        'custom_http_proxy': CUSTOM_HTTP_PROXY,
        'use_freeproxy': USE_FREEPROXY,
        'freeproxy_country': FREEPROXY_COUNTRY,
        'freeproxy_protocol': FREEPROXY_PROTOCOL,
        'freeproxy_random': FREEPROXY_RANDOM,
        'use_proxy_database': USE_PROXY_DATABASE,
        'proxy_database_path': PROXY_DATABASE_PATH,
        'chrome_proxy_args': CHROME_PROXY_ARGS,
        'disable_ssl_verification': DISABLE_SSL_VERIFICATION,
        'enable_direct_fallback': ENABLE_DIRECT_FALLBACK,
        'retry_failed_proxies': RETRY_FAILED_PROXIES,
        'verbose_proxy_logging': VERBOSE_PROXY_LOGGING,
        'test_proxy_before_use': TEST_PROXY_BEFORE_USE,
        'log_proxy_tests': LOG_PROXY_TESTS,
    }

def print_proxy_config():
    """Print current proxy configuration"""
    config = get_proxy_config()
    print("Current Proxy Configuration:")
    print("=" * 40)
    for key, value in config.items():
        print(f"{key}: {value}")
    print("=" * 40)

if __name__ == "__main__":
    print_proxy_config()
