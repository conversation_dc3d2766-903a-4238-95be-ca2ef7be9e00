#!/usr/bin/env python3
"""
VPN Dependencies Installer for Auto Gmail Creator
Helps install VPN clients and dependencies
"""

import subprocess
import sys
import platform
import os

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ {description} successful")
            return True
        else:
            print(f"   ❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ {description} failed: {e}")
        return False

def check_command_exists(command):
    """Check if a command exists in the system"""
    try:
        result = subprocess.run(f"which {command}", shell=True, capture_output=True)
        return result.returncode == 0
    except:
        return False

def install_protonvpn():
    """Install ProtonVPN CLI"""
    print("\n📦 Installing ProtonVPN CLI...")
    
    if check_command_exists("protonvpn"):
        print("   ✅ ProtonVPN CLI already installed")
        return True
    
    # Install via pip
    success = run_command("pip install protonvpn-cli", "Installing ProtonVPN CLI via pip")
    
    if success:
        print("   ℹ️  Next steps for ProtonVPN:")
        print("      1. Create account at https://protonvpn.com")
        print("      2. Run 'protonvpn init' to configure")
        print("      3. Test with 'protonvpn c --cc US'")
    
    return success

def install_openvpn_windows():
    """Install OpenVPN on Windows"""
    print("\n📦 Installing OpenVPN for Windows...")
    print("   ℹ️  Please download and install OpenVPN from:")
    print("      https://openvpn.net/community-downloads/")
    print("   ℹ️  After installation, restart your command prompt")
    return True

def install_openvpn_linux():
    """Install OpenVPN on Linux"""
    print("\n📦 Installing OpenVPN for Linux...")
    
    if check_command_exists("openvpn"):
        print("   ✅ OpenVPN already installed")
        return True
    
    # Try different package managers
    if check_command_exists("apt"):
        return run_command("sudo apt update && sudo apt install -y openvpn", "Installing OpenVPN via apt")
    elif check_command_exists("yum"):
        return run_command("sudo yum install -y openvpn", "Installing OpenVPN via yum")
    elif check_command_exists("dnf"):
        return run_command("sudo dnf install -y openvpn", "Installing OpenVPN via dnf")
    else:
        print("   ❌ No supported package manager found")
        print("   ℹ️  Please install OpenVPN manually for your distribution")
        return False

def install_openvpn_macos():
    """Install OpenVPN on macOS"""
    print("\n📦 Installing OpenVPN for macOS...")
    
    if check_command_exists("openvpn"):
        print("   ✅ OpenVPN already installed")
        return True
    
    if check_command_exists("brew"):
        return run_command("brew install openvpn", "Installing OpenVPN via Homebrew")
    else:
        print("   ❌ Homebrew not found")
        print("   ℹ️  Please install Homebrew first: https://brew.sh")
        print("   ℹ️  Or download OpenVPN from: https://openvpn.net/community-downloads/")
        return False

def install_wireguard_windows():
    """Install WireGuard on Windows"""
    print("\n📦 Installing WireGuard for Windows...")
    print("   ℹ️  Please download and install WireGuard from:")
    print("      https://www.wireguard.com/install/")
    return True

def install_wireguard_linux():
    """Install WireGuard on Linux"""
    print("\n📦 Installing WireGuard for Linux...")
    
    if check_command_exists("wg"):
        print("   ✅ WireGuard already installed")
        return True
    
    if check_command_exists("apt"):
        return run_command("sudo apt update && sudo apt install -y wireguard", "Installing WireGuard via apt")
    elif check_command_exists("yum"):
        return run_command("sudo yum install -y wireguard-tools", "Installing WireGuard via yum")
    elif check_command_exists("dnf"):
        return run_command("sudo dnf install -y wireguard-tools", "Installing WireGuard via dnf")
    else:
        print("   ❌ No supported package manager found")
        return False

def install_wireguard_macos():
    """Install WireGuard on macOS"""
    print("\n📦 Installing WireGuard for macOS...")
    
    if check_command_exists("wg"):
        print("   ✅ WireGuard already installed")
        return True
    
    if check_command_exists("brew"):
        return run_command("brew install wireguard-tools", "Installing WireGuard via Homebrew")
    else:
        print("   ❌ Homebrew not found")
        print("   ℹ️  Please install from Mac App Store or https://www.wireguard.com/install/")
        return False

def main():
    """Main installation function"""
    print("🚀 VPN Dependencies Installer")
    print("="*50)
    
    system = platform.system().lower()
    print(f"🖥️  Detected OS: {platform.system()} {platform.release()}")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ Python 3.6+ required")
        return False
    
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Install options
    print("\n📋 Available VPN options:")
    print("1. ProtonVPN CLI (Recommended - Free tier available)")
    print("2. OpenVPN (Free configs available)")
    print("3. WireGuard (Advanced users)")
    print("4. Install all")
    print("5. Skip VPN installation")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    success_count = 0
    total_count = 0
    
    if choice in ["1", "4"]:
        total_count += 1
        if install_protonvpn():
            success_count += 1
    
    if choice in ["2", "4"]:
        total_count += 1
        if system == "windows":
            if install_openvpn_windows():
                success_count += 1
        elif system == "linux":
            if install_openvpn_linux():
                success_count += 1
        elif system == "darwin":  # macOS
            if install_openvpn_macos():
                success_count += 1
    
    if choice in ["3", "4"]:
        total_count += 1
        if system == "windows":
            if install_wireguard_windows():
                success_count += 1
        elif system == "linux":
            if install_wireguard_linux():
                success_count += 1
        elif system == "darwin":  # macOS
            if install_wireguard_macos():
                success_count += 1
    
    if choice == "5":
        print("\n⏭️  Skipping VPN installation")
        print("   You can run the app with USE_VPN = False")
        return True
    
    # Summary
    print("\n" + "="*50)
    print("📊 INSTALLATION SUMMARY")
    print("="*50)
    
    if total_count == 0:
        print("❌ Invalid choice or no installations attempted")
        return False
    
    print(f"Successful installations: {success_count}/{total_count}")
    
    if success_count > 0:
        print("\n✅ Installation completed!")
        print("\n🔧 Next steps:")
        print("1. Run 'python vpn_test.py' to test your VPN setup")
        print("2. If tests pass, run 'python app.py' to start the main application")
        print("3. Check VPN_SETUP_GUIDE.md for detailed configuration instructions")
        return True
    else:
        print("\n❌ All installations failed")
        print("   Please check the error messages above")
        print("   Refer to VPN_SETUP_GUIDE.md for manual installation instructions")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
