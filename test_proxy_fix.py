#!/usr/bin/env python3
"""
Test script to verify proxy fixes work correctly
"""

import sys
import os
import requests
from fp.fp import FreeProxy
import csv
import random

# Add the current directory to path to import from app.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_proxy_connectivity(proxy_url, timeout=10):
    """Test if a proxy is working by making a simple HTTP request"""
    try:
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=timeout)
        if response.status_code == 200:
            print(f'✅ Proxy test successful: {proxy_url}')
            print(f'   Response: {response.json()}')
            return True
        else:
            print(f'❌ Proxy test failed with status {response.status_code}: {proxy_url}')
            return False
    except Exception as e:
        print(f'❌ Proxy test failed with error: {proxy_url} - {e}')
        return False

def test_direct_connection():
    """Test direct connection without proxy"""
    try:
        response = requests.get('http://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            print(f'✅ Direct connection successful')
            print(f'   Your IP: {response.json()}')
            return True
        else:
            print(f'❌ Direct connection failed with status {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ Direct connection failed: {e}')
        return False

def test_freeproxy():
    """Test FreeProxy library"""
    print("\n🔍 Testing FreeProxy library...")
    try:
        proxy = FreeProxy(timeout=5, rand=True).get()
        print(f"   Got proxy from FreeProxy: {proxy}")
        return test_proxy_connectivity(proxy, timeout=8)
    except Exception as e:
        print(f"❌ FreeProxy failed: {e}")
        return False

def test_proxy_database():
    """Test proxy database"""
    print("\n🔍 Testing proxy database...")
    try:
        with open("./data/Proxy_DB.csv", 'r') as proxy_list_file:
            proxy_list = list(csv.reader(proxy_list_file))
        
        if not proxy_list:
            print("❌ Proxy database is empty")
            return False
            
        # Test a few random proxies from the database
        for i in range(min(3, len(proxy_list))):
            proxy_entry = random.choice(proxy_list)
            proxy = "http://" + proxy_entry[0]
            print(f"   Testing proxy from DB: {proxy}")
            if test_proxy_connectivity(proxy, timeout=8):
                return True
        
        print("❌ No working proxies found in database")
        return False
    except Exception as e:
        print(f"❌ Proxy database test failed: {e}")
        return False

def test_driver_initialization():
    """Test Chrome driver initialization with the fixed setDriver function"""
    print("\n🔍 Testing Chrome driver initialization...")
    try:
        # Import the fixed setDriver function
        from app import setDriver
        
        print("   Initializing Chrome driver with proxy...")
        driver = setDriver()
        
        if driver:
            print("✅ Chrome driver initialized successfully")
            
            # Test navigation to a simple page
            try:
                driver.get("http://httpbin.org/ip")
                print("✅ Successfully navigated to test page")
                
                # Get page source to verify it loaded
                page_source = driver.page_source
                if "origin" in page_source.lower():
                    print("✅ Page loaded correctly")
                else:
                    print("⚠️  Page loaded but content may be incomplete")
                    
            except Exception as e:
                print(f"⚠️  Navigation test failed: {e}")
            finally:
                driver.quit()
                print("   Chrome driver closed")
            
            return True
        else:
            print("❌ Chrome driver initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Driver test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting proxy fix verification tests...\n")
    
    # Test 1: Direct connection
    print("=" * 50)
    print("TEST 1: Direct Connection")
    print("=" * 50)
    direct_ok = test_direct_connection()
    
    # Test 2: FreeProxy
    print("\n" + "=" * 50)
    print("TEST 2: FreeProxy Library")
    print("=" * 50)
    freeproxy_ok = test_freeproxy()
    
    # Test 3: Proxy Database
    print("\n" + "=" * 50)
    print("TEST 3: Proxy Database")
    print("=" * 50)
    proxydb_ok = test_proxy_database()
    
    # Test 4: Chrome Driver
    print("\n" + "=" * 50)
    print("TEST 4: Chrome Driver Initialization")
    print("=" * 50)
    driver_ok = test_driver_initialization()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Direct Connection: {'✅ PASS' if direct_ok else '❌ FAIL'}")
    print(f"FreeProxy Library: {'✅ PASS' if freeproxy_ok else '❌ FAIL'}")
    print(f"Proxy Database:    {'✅ PASS' if proxydb_ok else '❌ FAIL'}")
    print(f"Chrome Driver:     {'✅ PASS' if driver_ok else '❌ FAIL'}")
    
    if direct_ok and (freeproxy_ok or proxydb_ok) and driver_ok:
        print("\n🎉 All critical tests passed! The proxy fix should work.")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
