#!/usr/bin/env python3
"""
VPN Test Script for Auto Gmail Creator
Tests VPN functionality before running the main application
"""

import sys
import time
from vpn_manager import get_vpn_manager

def test_vpn_functionality():
    """Test VPN connection and IP changing"""
    print("🚀 Starting VPN Test...\n")
    
    try:
        # Initialize VPN manager
        print("1️⃣ Initializing VPN Manager...")
        vpn = get_vpn_manager("auto")
        print(f"   ✅ VPN Manager initialized: {vpn.vpn_type}")
        
        # Get original IP
        print("\n2️⃣ Getting original IP address...")
        original_ip = vpn.get_current_ip()
        if original_ip:
            print(f"   ✅ Original IP: {original_ip}")
        else:
            print("   ❌ Failed to get original IP")
            return False
        
        # Test internet connection
        print("\n3️⃣ Testing internet connection...")
        if vpn.test_connection():
            print("   ✅ Internet connection working")
        else:
            print("   ❌ Internet connection failed")
            return False
        
        # Connect to VPN
        print("\n4️⃣ Connecting to VPN...")
        if vpn.connect():
            print("   ✅ VPN connection successful")
            
            # Wait for connection to stabilize
            print("   ⏳ Waiting for connection to stabilize...")
            time.sleep(5)
            
            # Get new IP
            print("\n5️⃣ Getting new IP address...")
            new_ip = vpn.get_current_ip()
            if new_ip and new_ip != original_ip:
                print(f"   ✅ IP successfully changed: {original_ip} → {new_ip}")
            elif new_ip == original_ip:
                print(f"   ⚠️  IP didn't change (still {original_ip})")
                print("      This might be normal for some VPN configurations")
            else:
                print("   ❌ Failed to get new IP")
            
            # Test connection through VPN
            print("\n6️⃣ Testing connection through VPN...")
            if vpn.test_connection():
                print("   ✅ VPN connection working properly")
            else:
                print("   ❌ VPN connection not working")
            
            # Disconnect VPN
            print("\n7️⃣ Disconnecting VPN...")
            if vpn.disconnect():
                print("   ✅ VPN disconnected successfully")
                
                # Wait for disconnection
                time.sleep(5)
                
                # Verify IP restored
                print("\n8️⃣ Verifying IP restoration...")
                final_ip = vpn.get_current_ip()
                if final_ip == original_ip:
                    print(f"   ✅ IP restored to original: {final_ip}")
                else:
                    print(f"   ⚠️  IP changed to: {final_ip} (may take time to restore)")
                
                return True
            else:
                print("   ❌ VPN disconnection failed")
                return False
        else:
            print("   ❌ VPN connection failed")
            return False
            
    except Exception as e:
        print(f"   ❌ VPN test failed with error: {e}")
        return False

def test_chrome_with_vpn():
    """Test Chrome driver with VPN"""
    print("\n" + "="*50)
    print("🌐 Testing Chrome Driver with VPN")
    print("="*50)
    
    try:
        # Import the updated setDriver function
        from app import setDriver, USE_VPN
        
        if not USE_VPN:
            print("⚠️  VPN is disabled in app.py")
            print("   Set USE_VPN = True to test VPN functionality")
            return True
        
        # Initialize VPN
        vpn = get_vpn_manager("auto")
        
        # Connect to VPN
        print("1️⃣ Connecting to VPN for Chrome test...")
        if vpn.connect():
            print("   ✅ VPN connected")
            
            # Initialize Chrome driver
            print("\n2️⃣ Initializing Chrome driver with VPN...")
            driver = setDriver(vpn)
            
            if driver:
                print("   ✅ Chrome driver initialized successfully")
                
                # Test navigation
                print("\n3️⃣ Testing navigation...")
                try:
                    driver.get("https://httpbin.org/ip")
                    time.sleep(3)
                    
                    # Check if page loaded
                    if "origin" in driver.page_source.lower():
                        print("   ✅ Navigation successful")
                        print("   ✅ Page loaded correctly")
                    else:
                        print("   ⚠️  Page loaded but content may be incomplete")
                    
                except Exception as e:
                    print(f"   ❌ Navigation failed: {e}")
                
                # Close driver
                print("\n4️⃣ Closing Chrome driver...")
                driver.quit()
                print("   ✅ Chrome driver closed")
                
                # Disconnect VPN
                print("\n5️⃣ Disconnecting VPN...")
                vpn.disconnect()
                print("   ✅ VPN disconnected")
                
                return True
            else:
                print("   ❌ Chrome driver initialization failed")
                vpn.disconnect()
                return False
        else:
            print("   ❌ VPN connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Chrome + VPN test failed: {e}")
        return False

def main():
    """Run all VPN tests"""
    print("🔧 VPN Test Suite for Auto Gmail Creator")
    print("="*50)
    
    # Test 1: Basic VPN functionality
    vpn_test_passed = test_vpn_functionality()
    
    # Test 2: Chrome driver with VPN
    chrome_test_passed = test_chrome_with_vpn()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    print(f"VPN Functionality:     {'✅ PASS' if vpn_test_passed else '❌ FAIL'}")
    print(f"Chrome + VPN:          {'✅ PASS' if chrome_test_passed else '❌ FAIL'}")
    
    if vpn_test_passed and chrome_test_passed:
        print("\n🎉 All tests passed! Your VPN setup is working correctly.")
        print("   You can now run 'python app.py' to start creating Gmail accounts.")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        print("   Refer to VPN_SETUP_GUIDE.md for troubleshooting steps.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
