#!/usr/bin/env python3
"""
VPN Manager for Auto Gmail Creator
Manages VPN connections instead of using proxy databases
"""

import subprocess
import time
import requests
import json
import os
import random
from typing import Optional, Dict, List

class VPNManager:
    def __init__(self):
        self.current_vpn = None
        self.vpn_type = None
        self.original_ip = None
        
    def get_current_ip(self) -> Optional[str]:
        """Get current public IP address"""
        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                ip = response.json().get('origin', '').split(',')[0].strip()
                print(f"Current IP: {ip}")
                return ip
        except Exception as e:
            print(f"Failed to get IP: {e}")
            
        # Fallback IP service
        try:
            response = requests.get('https://api.ipify.org?format=json', timeout=10)
            if response.status_code == 200:
                ip = response.json().get('ip', '')
                print(f"Current IP (fallback): {ip}")
                return ip
        except Exception as e:
            print(f"Fallback IP service failed: {e}")
            
        return None
    
    def test_connection(self) -> bool:
        """Test if internet connection is working"""
        try:
            response = requests.get('https://www.google.com', timeout=10)
            return response.status_code == 200
        except:
            return False

class ProtonVPNManager(VPNManager):
    """Manager for ProtonVPN (free tier available)"""
    
    def __init__(self):
        super().__init__()
        self.vpn_type = "ProtonVPN"
        
    def connect(self, country: str = "random") -> bool:
        """Connect to ProtonVPN"""
        try:
            if country == "random":
                countries = ["US", "NL", "JP"]  # Free ProtonVPN countries
                country = random.choice(countries)
            
            print(f"Connecting to ProtonVPN ({country})...")
            
            # ProtonVPN CLI command (requires protonvpn-cli to be installed)
            cmd = f"protonvpn c --cc {country}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"Successfully connected to ProtonVPN ({country})")
                self.current_vpn = country
                time.sleep(5)  # Wait for connection to stabilize
                return self.test_connection()
            else:
                print(f"ProtonVPN connection failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"ProtonVPN connection error: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from ProtonVPN"""
        try:
            print("Disconnecting from ProtonVPN...")
            result = subprocess.run("protonvpn d", shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("Successfully disconnected from ProtonVPN")
                self.current_vpn = None
                return True
            else:
                print(f"ProtonVPN disconnection failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"ProtonVPN disconnection error: {e}")
            return False

class OpenVPNManager(VPNManager):
    """Manager for OpenVPN with free VPN configs"""
    
    def __init__(self):
        super().__init__()
        self.vpn_type = "OpenVPN"
        self.config_dir = "./vpn_configs"
        self.current_process = None
        
    def download_free_configs(self) -> bool:
        """Download free VPN configs from VPNGate or similar services"""
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            
            # VPNGate API for free OpenVPN configs
            print("Downloading free VPN configurations...")
            response = requests.get('http://www.vpngate.net/api/iphone/', timeout=30)
            
            if response.status_code == 200:
                lines = response.text.strip().split('\n')[2:]  # Skip header lines
                configs_downloaded = 0
                
                for line in lines[:10]:  # Download first 10 configs
                    parts = line.split(',')
                    if len(parts) > 14:
                        country = parts[6]
                        config_data = parts[14]
                        
                        if config_data:
                            try:
                                import base64
                                config_content = base64.b64decode(config_data).decode('utf-8')
                                
                                config_file = os.path.join(self.config_dir, f"{country}_{configs_downloaded}.ovpn")
                                with open(config_file, 'w') as f:
                                    f.write(config_content)
                                
                                configs_downloaded += 1
                                print(f"Downloaded config for {country}")
                                
                            except Exception as e:
                                print(f"Failed to process config: {e}")
                
                print(f"Downloaded {configs_downloaded} VPN configurations")
                return configs_downloaded > 0
            
        except Exception as e:
            print(f"Failed to download VPN configs: {e}")
            
        return False
    
    def connect(self, config_file: str = None) -> bool:
        """Connect using OpenVPN"""
        try:
            if not config_file:
                # Choose random config file
                config_files = [f for f in os.listdir(self.config_dir) if f.endswith('.ovpn')]
                if not config_files:
                    print("No VPN config files found. Downloading...")
                    if not self.download_free_configs():
                        return False
                    config_files = [f for f in os.listdir(self.config_dir) if f.endswith('.ovpn')]
                
                config_file = random.choice(config_files)
            
            config_path = os.path.join(self.config_dir, config_file)
            print(f"Connecting to OpenVPN using {config_file}...")
            
            # Start OpenVPN process
            cmd = f"openvpn --config {config_path} --daemon"
            self.current_process = subprocess.Popen(cmd, shell=True)
            
            # Wait for connection
            time.sleep(15)
            
            if self.test_connection():
                print(f"Successfully connected via OpenVPN ({config_file})")
                self.current_vpn = config_file
                return True
            else:
                print("OpenVPN connection failed")
                self.disconnect()
                return False
                
        except Exception as e:
            print(f"OpenVPN connection error: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect OpenVPN"""
        try:
            print("Disconnecting OpenVPN...")
            
            # Kill OpenVPN processes
            subprocess.run("pkill openvpn", shell=True)
            
            if self.current_process:
                self.current_process.terminate()
                self.current_process = None
            
            time.sleep(5)
            self.current_vpn = None
            print("OpenVPN disconnected")
            return True
            
        except Exception as e:
            print(f"OpenVPN disconnection error: {e}")
            return False

class WireGuardManager(VPNManager):
    """Manager for WireGuard VPN"""
    
    def __init__(self):
        super().__init__()
        self.vpn_type = "WireGuard"
        
    def connect(self, config_name: str = "wg0") -> bool:
        """Connect using WireGuard"""
        try:
            print(f"Connecting to WireGuard ({config_name})...")
            
            # Start WireGuard interface
            result = subprocess.run(f"wg-quick up {config_name}", shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"Successfully connected to WireGuard ({config_name})")
                self.current_vpn = config_name
                time.sleep(5)
                return self.test_connection()
            else:
                print(f"WireGuard connection failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"WireGuard connection error: {e}")
            return False
    
    def disconnect(self, config_name: str = "wg0") -> bool:
        """Disconnect WireGuard"""
        try:
            print(f"Disconnecting WireGuard ({config_name})...")
            result = subprocess.run(f"wg-quick down {config_name}", shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Successfully disconnected from WireGuard")
                self.current_vpn = None
                return True
            else:
                print(f"WireGuard disconnection failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"WireGuard disconnection error: {e}")
            return False

def get_vpn_manager(vpn_type: str = "auto") -> VPNManager:
    """Get appropriate VPN manager"""
    
    if vpn_type == "auto":
        # Auto-detect available VPN clients
        if subprocess.run("which protonvpn", shell=True, capture_output=True).returncode == 0:
            print("ProtonVPN CLI detected")
            return ProtonVPNManager()
        elif subprocess.run("which openvpn", shell=True, capture_output=True).returncode == 0:
            print("OpenVPN detected")
            return OpenVPNManager()
        elif subprocess.run("which wg", shell=True, capture_output=True).returncode == 0:
            print("WireGuard detected")
            return WireGuardManager()
        else:
            print("No VPN client detected, using OpenVPN with free configs")
            return OpenVPNManager()
    
    elif vpn_type.lower() == "protonvpn":
        return ProtonVPNManager()
    elif vpn_type.lower() == "openvpn":
        return OpenVPNManager()
    elif vpn_type.lower() == "wireguard":
        return WireGuardManager()
    else:
        raise ValueError(f"Unknown VPN type: {vpn_type}")

# Example usage
if __name__ == "__main__":
    # Test VPN manager
    vpn = get_vpn_manager()
    
    print("Getting original IP...")
    original_ip = vpn.get_current_ip()
    
    print("Connecting to VPN...")
    if vpn.connect():
        new_ip = vpn.get_current_ip()
        print(f"IP changed from {original_ip} to {new_ip}")
        
        time.sleep(5)
        
        print("Disconnecting from VPN...")
        vpn.disconnect()
        
        final_ip = vpn.get_current_ip()
        print(f"IP restored to {final_ip}")
    else:
        print("VPN connection failed")
