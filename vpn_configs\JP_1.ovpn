###############################################################################

# OpenVPN 2.0 Sample Configuration File

# for PacketiX VPN / SoftEther VPN Server

# 

# !!! AUTO-GENERATED BY SOFTETHER VPN SERVER MANAGEMENT TOOL !!!

# 

# !!! YOU HAVE TO REVIEW IT BEFORE USE AND MODIFY IT AS NECESSARY !!!

# 

# This configuration file is auto-generated. You might use this config file

# in order to connect to the PacketiX VPN / SoftEther VPN Server.

# However, before you try it, you should review the descriptions of the file

# to determine the necessity to modify to suitable for your real environment.

# If necessary, you have to modify a little adequately on the file.

# For example, the IP address or the hostname as a destination VPN Server

# should be confirmed.

# 

# Note that to use OpenVPN 2.0, you have to put the certification file of

# the destination VPN Server on the OpenVPN Client computer when you use this

# config file. Please refer the below descriptions carefully.





###############################################################################

# Specify the type of the layer of the VPN connection.

# 

# To connect to the VPN Server as a "Remote-Access VPN Client PC",

#  specify 'dev tun'. (Layer-3 IP Routing Mode)

#

# To connect to the VPN Server as a bridging equipment of "Site-to-Site VPN",

#  specify 'dev tap'. (Layer-2 Ethernet Bridgine Mode)



dev tun





###############################################################################

# Specify the underlying protocol beyond the Internet.

# Note that this setting must be correspond with the listening setting on

# the VPN Server.

# 

# Specify either 'proto tcp' or 'proto udp'.



proto tcp





###############################################################################

# The destination hostname / IP address, and port number of

# the target VPN Server.

# 

# You have to specify as 'remote <HOSTNAME> <PORT>'. You can also

# specify the IP address instead of the hostname.

# 

# Note that the auto-generated below hostname are a "auto-detected

# IP address" of the VPN Server. You have to confirm the correctness

# beforehand.

# 

# When you want to connect to the VPN Server by using TCP protocol,

# the port number of the destination TCP port should be same as one of

# the available TCP listeners on the VPN Server.

# 

# When you use UDP protocol, the port number must same as the configuration

# setting of "OpenVPN Server Compatible Function" on the VPN Server.



remote ************** 443





###############################################################################

# The HTTP/HTTPS proxy setting.

# 

# Only if you have to use the Internet via a proxy, uncomment the below

# two lines and specify the proxy address and the port number.

# In the case of using proxy-authentication, refer the OpenVPN manual.



;http-proxy-retry

;http-proxy [proxy server] [proxy port]





###############################################################################

# The encryption and authentication algorithm.

# 

# Default setting is good. Modify it as you prefer.

# When you specify an unsupported algorithm, the error will occur.

# 

# The supported algorithms are as follows:

#  cipher: [NULL-CIPHER] NULL AES-128-CBC AES-192-CBC AES-256-CBC BF-CBC

#          CAST-CBC CAST5-CBC DES-CBC DES-EDE-CBC DES-EDE3-CBC DESX-CBC

#          RC2-40-CBC RC2-64-CBC RC2-CBC

#  auth:   SHA SHA1 MD5 MD4 RMD160



cipher AES-128-CBC

auth SHA1





###############################################################################

# Other parameters necessary to connect to the VPN Server.

# 

# It is not recommended to modify it unless you have a particular need.



resolv-retry infinite

nobind

persist-key

persist-tun

client

verb 3

#auth-user-pass





###############################################################################

# The certificate file of the destination VPN Server.

# 

# The CA certificate file is embedded in the inline format.

# You can replace this CA contents if necessary.

# Please note that if the server certificate is not a self-signed, you have to

# specify the signer's root certificate (CA) here.



<ca>

-----BEGIN CERTIFICATE-----

MIIFazCCA1OgAwIBAgIRAIIQz7DSQONZRGPgu2OCiwAwDQYJKoZIhvcNAQELBQAw

TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh

cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMTUwNjA0MTEwNDM4

WhcNMzUwNjA0MTEwNDM4WjBPMQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJu

ZXQgU2VjdXJpdHkgUmVzZWFyY2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBY

MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAK3oJHP0FDfzm54rVygc

h77ct984kIxuPOZXoHj3dcKi/vVqbvYATyjb3miGbESTtrFj/RQSa78f0uoxmyF+

0TM8ukj13Xnfs7j/EvEhmkvBioZxaUpmZmyPfjxwv60pIgbz5MDmgK7iS4+3mX6U

A5/TR5d8mUgjU+g4rk8Kb4Mu0UlXjIB0ttov0DiNewNwIRt18jA8+o+u3dpjq+sW

T8KOEUt+zwvo/7V3LvSye0rgTBIlDHCNAymg4VMk7BPZ7hm/ELNKjD+Jo2FR3qyH

B5T0Y3HsLuJvW5iB4YlcNHlsdu87kGJ55tukmi8mxdAQ4Q7e2RCOFvu396j3x+UC

B5iPNgiV5+I3lg02dZ77DnKxHZu8A/lJBdiB3QW0KtZB6awBdpUKD9jf1b0SHzUv

KBds0pjBqAlkd25HN7rOrFleaJ1/ctaJxQZBKT5ZPt0m9STJEadao0xAH0ahmbWn

OlFuhjuefXKnEgV4We0+UXgVCwOPjdAvBbI+e0ocS3MFEvzG6uBQE3xDk3SzynTn

jh8BCNAw1FtxNrQHusEwMFxIt4I7mKZ9YIqioymCzLq9gwQbooMDQaHWBfEbwrbw

qHyGO0aoSCqI3Haadr8faqU9GY/rOPNk3sgrDQoo//fb4hVC1CLQJ13hef4Y53CI

rU7m2Ys6xt0nUW7/vGT1M0NPAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV

HRMBAf8EBTADAQH/MB0GA1UdDgQWBBR5tFnme7bl5AFzgAiIyBpY9umbbjANBgkq

hkiG9w0BAQsFAAOCAgEAVR9YqbyyqFDQDLHYGmkgJykIrGF1XIpu+ILlaS/V9lZL

ubhzEFnTIZd+50xx+7LSYK05qAvqFyFWhfFQDlnrzuBZ6brJFe+GnY+EgPbk6ZGQ

3BebYhtF8GaV0nxvwuo77x/Py9auJ/GpsMiu/X1+mvoiBOv/2X/qkSsisRcOj/KK

NFtY2PwByVS5uCbMiogziUwthDyC3+6WVwW6LLv3xLfHTjuCvjHIInNzktHCgKQ5

ORAzI4JMPJ+GslWYHb4phowim57iaztXOoJwTdwJx4nLCgdNbOhdjsnvzqvHu7Ur

TkXWStAmzOVyyghqpZXjFaH3pO3JLF+l+/+sKAIuvtd7u+Nxe5AW0wdeRlN8NwdC

jNPElpzVmbUq4JUagEiuTDkHzsxHpFKVK7q4+63SM1N95R1NbdWhscdCb+ZAJzVc

oyi3B43njTOQ5yOf+1CceWxG1bQVs5ZufpsMljq4Ui0/1lvh+wjChP4kqKOJ2qxq

4RgqsahDYVvTH9w7jXbyLeiNdd8XM2w9U/t7y0Ff/9yi0GE44Za4rF2LN9d11TPA

mRGunUHBcnWEvgJBQl9nJEiU0Zsnvgc/ubhPgXRR4Xq37Z0j4r7g1SgEEzwxA57d

emyPxgcYxn/eR44/KJ4EBs+lVDR3veyJm+kXQ99b21/+jh5Xos1AnX5iItreGCc=

-----END CERTIFICATE-----



</ca>





###############################################################################

# The client certificate file (dummy).

# 

# In some implementations of OpenVPN Client software

# (for example: OpenVPN Client for iOS),

# a pair of client certificate and private key must be included on the

# configuration file due to the limitation of the client.

# So this sample configuration file has a dummy pair of client certificate

# and private key as follows.



<cert>

-----BEGIN CERTIFICATE-----

MIICxjCCAa4CAQAwDQYJKoZIhvcNAQEFBQAwKTEaMBgGA1UEAxMRVlBOR2F0ZUNs

aWVudENlcnQxCzAJBgNVBAYTAkpQMB4XDTEzMDIxMTAzNDk0OVoXDTM3MDExOTAz

MTQwN1owKTEaMBgGA1UEAxMRVlBOR2F0ZUNsaWVudENlcnQxCzAJBgNVBAYTAkpQ

MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5h2lgQQYUjwoKYJbzVZA

5VcIGd5otPc/qZRMt0KItCFA0s9RwReNVa9fDRFLRBhcITOlv3FBcW3E8h1Us7RD

4W8GmJe8zapJnLsD39OSMRCzZJnczW4OCH1PZRZWKqDtjlNca9AF8a65jTmlDxCQ

CjntLIWk5OLLVkFt9/tScc1GDtci55ofhaNAYMPiH7V8+1g66pGHXAoWK6AQVH67

XCKJnGB5nlQ+HsMYPV/O49Ld91ZN/2tHkcaLLyNtywxVPRSsRh480jju0fcCsv6h

p/0yXnTB//mWutBGpdUlIbwiITbAmrsbYnjigRvnPqX1RNJUbi9Fp6C2c/HIFJGD

ywIDAQABMA0GCSqGSIb3DQEBBQUAA4IBAQChO5hgcw/4oWfoEFLu9kBa1B//kxH8

hQkChVNn8BRC7Y0URQitPl3DKEed9URBDdg2KOAz77bb6ENPiliD+a38UJHIRMqe

UBHhllOHIzvDhHFbaovALBQceeBzdkQxsKQESKmQmR832950UCovoyRB61UyAV7h

+mZhYPGRKXKSJI6s0Egg/Cri+Cwk4bjJfrb5hVse11yh4D9MHhwSfCOH+0z4hPUT

Fku7dGavURO5SVxMn/sL6En5D+oSeXkadHpDs+Airym2YHh15h0+jPSOoR6yiVp/

6zZeZkrN43kuS73KpKDFjfFPh8t4r1gOIjttkNcQqBccusnplQ7HJpsk

-----END CERTIFICATE-----



</cert>



<key>

************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************



</key>



